#!/usr/bin/env python3
"""
Simple test script to verify backend dependencies and start the server
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported"""
    try:
        import fastapi
        print(f"✅ FastAPI {fastapi.__version__} imported successfully")
        
        import uvicorn
        print(f"✅ Uvicorn imported successfully")
        
        import fitz  # PyMuPDF
        print(f"✅ PyMuPDF {fitz.version[0]} imported successfully")
        
        import google.generativeai as genai
        print(f"✅ Google Generative AI imported successfully")
        
        from dotenv import load_dotenv
        print(f"✅ python-dotenv imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_app():
    """Test if the FastAPI app can be imported"""
    try:
        sys.path.append(os.path.dirname(__file__))
        from app.main import app
        print(f"✅ FastAPI app imported successfully")
        return True
    except Exception as e:
        print(f"❌ App import error: {e}")
        return False

def start_server():
    """Start the FastAPI server"""
    try:
        import uvicorn
        from app.main import app
        
        print("🚀 Starting FastAPI server...")
        print("📍 Server will be available at: http://localhost:8000")
        print("📖 API docs will be available at: http://localhost:8000/docs")
        print("🔄 Press Ctrl+C to stop the server")
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=True
        )
    except Exception as e:
        print(f"❌ Server start error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing All PDF Tools Backend...")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed!")
        sys.exit(1)
    
    print()
    
    # Test app
    if not test_app():
        print("❌ App tests failed!")
        sys.exit(1)
    
    print()
    print("✅ All tests passed! Starting server...")
    print("=" * 50)
    
    # Start server
    start_server()
