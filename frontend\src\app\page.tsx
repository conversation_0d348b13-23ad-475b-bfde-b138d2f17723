import Link from "next/link";
import {
  FileText,
  <PERSON>issors,
  Merge,
  Shield,
  ShieldOff,
  Compress,
  MessageSquare,
  FileImage,
  Settings,
  Sparkles
} from "lucide-react";

const pdfTools = [
  {
    title: "Merge PDF",
    description: "Combine multiple PDF files into one document",
    icon: Merge,
    href: "/merge",
    category: "organize"
  },
  {
    title: "Split PDF",
    description: "Split PDF into multiple documents or extract pages",
    icon: Scissors,
    href: "/split",
    category: "organize"
  },
  {
    title: "Convert Images",
    description: "Convert JPG, PNG, WEBP to PDF or PDF to images",
    icon: FileImage,
    href: "/convert",
    category: "convert"
  },
  {
    title: "Compress PDF",
    description: "Reduce PDF file size while maintaining quality",
    icon: Compress,
    href: "/compress",
    category: "optimize"
  },
  {
    title: "Protect PDF",
    description: "Add password protection to your PDF files",
    icon: Shield,
    href: "/protect",
    category: "security"
  },
  {
    title: "Unlock PDF",
    description: "Remove password protection from PDF files",
    icon: <PERSON><PERSON><PERSON>,
    href: "/unlock",
    category: "security"
  },
  {
    title: "Chat with PDF",
    description: "Ask questions about your PDF content using AI",
    icon: MessageSquare,
    href: "/chat",
    category: "ai",
    badge: "AI"
  },
  {
    title: "Summarize PDF",
    description: "Generate AI-powered summaries of your documents",
    icon: Sparkles,
    href: "/summarize",
    category: "ai",
    badge: "AI"
  }
];

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                All PDF Tools
              </h1>
            </div>
            <Link
              href="/settings"
              className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Settings</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Your Complete PDF Toolkit
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Merge, split, convert, compress, and unlock the power of AI with your PDF documents.
            All tools work directly in your browser for maximum privacy and speed.
          </p>
        </div>

        {/* PDF Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {pdfTools.map((tool) => {
            const IconComponent = tool.icon;
            return (
              <Link
                key={tool.href}
                href={tool.href}
                className="group relative bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
              >
                {tool.badge && (
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
                    {tool.badge}
                  </div>
                )}
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50 transition-colors">
                    <IconComponent className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {tool.title}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  {tool.description}
                </p>
              </Link>
            );
          })}
        </div>

        {/* Features Section */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="bg-green-100 dark:bg-green-900/30 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              100% Secure
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              All processing happens in your browser. Your files never leave your device for standard operations.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-blue-100 dark:bg-blue-900/30 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              AI-Powered
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Chat with your PDFs and generate summaries using advanced AI technology.
            </p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 dark:bg-purple-900/30 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Always Free
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              All tools are completely free to use with no hidden costs or subscription fees.
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400">
              © 2025 All PDF Tools. Created by{" "}
              <a
                href="https://github.com/chirag127"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                Chirag Singhal
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
