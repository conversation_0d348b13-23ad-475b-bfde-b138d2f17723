"""
Pydantic models for All PDF Tools API
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict
from enum import Enum

class ModelInfo(BaseModel):
    """Information about a Gemini model"""
    name: str
    description: str
    input_token_limit: int
    output_token_limit: int

class ChatMessage(BaseModel):
    """Chat message structure"""
    role: str = Field(..., description="Role of the message sender (user/assistant)")
    content: str = Field(..., description="Content of the message")

class ChatRequest(BaseModel):
    """Request model for chat with PDF"""
    query: str = Field(..., description="User's question about the PDF")
    history: List[ChatMessage] = Field(default=[], description="Previous conversation history")
    model: str = Field(default="gemini-pro", description="Gemini model to use")

class ChatResponse(BaseModel):
    """Response model for chat with PDF"""
    response: str = Field(..., description="AI response to the user's query")
    sources: List[str] = Field(default=[], description="Source chunks used for the response")

class SummarizeRequest(BaseModel):
    """Request model for PDF summarization"""
    model: str = Field(default="gemini-pro", description="Gemini model to use")
    summary_type: str = Field(default="comprehensive", description="Type of summary (brief/comprehensive)")

class SummarizeResponse(BaseModel):
    """Response model for PDF summarization"""
    summary: str = Field(..., description="Generated summary of the PDF")
    word_count: int = Field(..., description="Word count of the original document")
    summary_word_count: int = Field(..., description="Word count of the summary")

class ProtectRequest(BaseModel):
    """Request model for PDF protection"""
    password: str = Field(..., description="Password to protect the PDF with")

class UnlockRequest(BaseModel):
    """Request model for PDF unlock"""
    password: str = Field(..., description="Password to unlock the PDF")

class ProcessingStatus(BaseModel):
    """Status of file processing"""
    status: str = Field(..., description="Processing status (processing/completed/error)")
    message: str = Field(..., description="Status message")
    progress: Optional[int] = Field(None, description="Progress percentage (0-100)")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")

class SuccessResponse(BaseModel):
    """Generic success response"""
    success: bool = Field(True, description="Operation success status")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional response data")
