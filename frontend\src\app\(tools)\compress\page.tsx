"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Download, Compress, FileText, Info, CheckCircle } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FileUploader from '@/components/FileUploader';
import { downloadBlob, generateFilename, formatFileSize, handleApiError } from '@/lib/utils';
import { apiService } from '@/lib/api';

interface CompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  blob: Blob;
  filename: string;
}

export default function CompressPDFPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<CompressionResult | null>(null);

  const handleFileSelect = (files: File[]) => {
    setSelectedFile(files[0] || null);
    setError(null);
    setResult(null);
  };

  const compressPDF = async () => {
    if (!selectedFile) {
      setError('Please select a PDF file to compress.');
      return;
    }

    setProcessing(true);
    setError(null);
    setResult(null);

    try {
      const blob = await apiService.compressPdf(selectedFile);
      
      const originalSize = selectedFile.size;
      const compressedSize = blob.size;
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
      
      const filename = generateFilename(selectedFile.name, 'compressed');
      
      setResult({
        originalSize,
        compressedSize,
        compressionRatio,
        blob,
        filename
      });

    } catch (err) {
      console.error('Error compressing PDF:', err);
      setError(handleApiError(err));
    } finally {
      setProcessing(false);
    }
  };

  const downloadCompressed = () => {
    if (result) {
      downloadBlob(result.blob, result.filename);
    }
  };

  const resetTool = () => {
    setSelectedFile(null);
    setResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Tools</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Compress className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Compress PDF
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Reduce your PDF file size while maintaining quality. Perfect for sharing or storage.
          </p>
        </div>

        <div className="space-y-6">
          {!result ? (
            <>
              {/* File Upload */}
              <Card>
                <CardHeader>
                  <CardTitle>Select PDF File</CardTitle>
                  <CardDescription>
                    Choose a PDF file to compress. The compression will reduce file size while preserving quality.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FileUploader
                    onFileSelect={handleFileSelect}
                    acceptedFileTypes="PDF"
                    multiple={false}
                    selectedFiles={selectedFile ? [selectedFile] : []}
                  />
                </CardContent>
              </Card>

              {/* File Info */}
              {selectedFile && (
                <Card>
                  <CardHeader>
                    <CardTitle>File Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded">
                        <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {selectedFile.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Original size: {formatFileSize(selectedFile.size)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Error Display */}
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-red-800 dark:text-red-200">{error}</p>
                </div>
              )}

              {/* Compress Button */}
              <div className="flex justify-center">
                <Button
                  onClick={compressPDF}
                  loading={processing}
                  disabled={!selectedFile}
                  size="lg"
                  className="min-w-[200px]"
                >
                  <Compress className="mr-2 h-5 w-5" />
                  {processing ? 'Compressing...' : 'Compress PDF'}
                </Button>
              </div>
            </>
          ) : (
            /* Results */
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span>Compression Complete!</span>
                </CardTitle>
                <CardDescription>
                  Your PDF has been successfully compressed.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Compression Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Original Size</p>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {formatFileSize(result.originalSize)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Compressed Size</p>
                    <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                      {formatFileSize(result.compressedSize)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Size Reduction</p>
                    <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                      {result.compressionRatio.toFixed(1)}%
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={downloadCompressed}
                    size="lg"
                    className="min-w-[200px]"
                  >
                    <Download className="mr-2 h-5 w-5" />
                    Download Compressed PDF
                  </Button>
                  <Button
                    onClick={resetTool}
                    variant="outline"
                    size="lg"
                  >
                    Compress Another File
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  About PDF Compression:
                </h3>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Compression reduces file size by optimizing images and removing redundant data</li>
                  <li>• Text quality is preserved while image quality may be slightly reduced</li>
                  <li>• Compression ratio depends on the original file content and structure</li>
                  <li>• Files are processed securely on our servers and immediately deleted</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
