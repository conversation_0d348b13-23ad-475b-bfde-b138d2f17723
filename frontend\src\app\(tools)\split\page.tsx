"use client";

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { ArrowLeft, Download, Scissors, FileText, Info } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FileUploader from '@/components/FileUploader';
import VisualPageManager from '@/components/VisualPageManager';
import { downloadBlob, generateFilename } from '@/lib/utils';
import { PDFDocument } from 'pdf-lib';

type SplitMode = 'extract' | 'remove' | 'range';

export default function SplitPDFPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedPages, setSelectedPages] = useState<number[]>([]);
  const [splitMode, setSplitMode] = useState<SplitMode>('extract');
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (files: File[]) => {
    setSelectedFile(files[0] || null);
    setSelectedPages([]);
    setError(null);
  };

  const handlePagesChange = (pages: number[]) => {
    setSelectedPages(pages);
  };

  const processPDF = async () => {
    if (!selectedFile) {
      setError('Please select a PDF file.');
      return;
    }

    if (selectedPages.length === 0) {
      setError('Please select at least one page.');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      const arrayBuffer = await selectedFile.arrayBuffer();
      const originalPdf = await PDFDocument.load(arrayBuffer);
      const totalPages = originalPdf.getPageCount();

      if (splitMode === 'extract') {
        // Create new PDF with selected pages
        const newPdf = await PDFDocument.create();
        const pagesToCopy = selectedPages.map(pageNum => pageNum - 1); // Convert to 0-based
        const copiedPages = await newPdf.copyPages(originalPdf, pagesToCopy);
        
        copiedPages.forEach(page => {
          newPdf.addPage(page);
        });

        const pdfBytes = await newPdf.save();
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const filename = generateFilename(selectedFile.name, `extracted_pages_${selectedPages.join('-')}`);
        downloadBlob(blob, filename);

      } else if (splitMode === 'remove') {
        // Create new PDF without selected pages
        const newPdf = await PDFDocument.create();
        const pagesToKeep: number[] = [];
        
        for (let i = 1; i <= totalPages; i++) {
          if (!selectedPages.includes(i)) {
            pagesToKeep.push(i - 1); // Convert to 0-based
          }
        }

        if (pagesToKeep.length === 0) {
          setError('Cannot remove all pages. At least one page must remain.');
          return;
        }

        const copiedPages = await newPdf.copyPages(originalPdf, pagesToKeep);
        copiedPages.forEach(page => {
          newPdf.addPage(page);
        });

        const pdfBytes = await newPdf.save();
        const blob = new Blob([pdfBytes], { type: 'application/pdf' });
        const filename = generateFilename(selectedFile.name, `removed_pages_${selectedPages.join('-')}`);
        downloadBlob(blob, filename);

      } else if (splitMode === 'range') {
        // Split into multiple files based on selected pages as breakpoints
        const sortedPages = [...selectedPages].sort((a, b) => a - b);
        let startPage = 1;

        for (let i = 0; i <= sortedPages.length; i++) {
          const endPage = i < sortedPages.length ? sortedPages[i] : totalPages;
          
          if (startPage <= endPage) {
            const newPdf = await PDFDocument.create();
            const pageRange: number[] = [];
            
            for (let page = startPage; page <= endPage; page++) {
              pageRange.push(page - 1); // Convert to 0-based
            }

            const copiedPages = await newPdf.copyPages(originalPdf, pageRange);
            copiedPages.forEach(page => {
              newPdf.addPage(page);
            });

            const pdfBytes = await newPdf.save();
            const blob = new Blob([pdfBytes], { type: 'application/pdf' });
            const filename = generateFilename(
              selectedFile.name, 
              `part_${i + 1}_pages_${startPage}-${endPage}`
            );
            downloadBlob(blob, filename);
          }

          startPage = endPage + 1;
        }
      }

    } catch (err) {
      console.error('Error processing PDF:', err);
      setError('Failed to process PDF. Please ensure the file is a valid PDF.');
    } finally {
      setProcessing(false);
    }
  };

  const getModeDescription = () => {
    switch (splitMode) {
      case 'extract':
        return 'Create a new PDF containing only the selected pages.';
      case 'remove':
        return 'Create a new PDF with the selected pages removed.';
      case 'range':
        return 'Split the PDF into multiple files using selected pages as breakpoints.';
      default:
        return '';
    }
  };

  const getButtonText = () => {
    switch (splitMode) {
      case 'extract':
        return processing ? 'Extracting...' : 'Extract Pages';
      case 'remove':
        return processing ? 'Removing...' : 'Remove Pages';
      case 'range':
        return processing ? 'Splitting...' : 'Split PDF';
      default:
        return 'Process';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Tools</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Scissors className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Split PDF
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Extract specific pages, remove unwanted pages, or split your PDF into multiple documents.
          </p>
        </div>

        <div className="space-y-6">
          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Select PDF File</CardTitle>
              <CardDescription>
                Choose a PDF file to split or extract pages from.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploader
                onFileSelect={handleFileSelect}
                acceptedFileTypes="PDF"
                multiple={false}
                selectedFiles={selectedFile ? [selectedFile] : []}
              />
            </CardContent>
          </Card>

          {/* Split Mode Selection */}
          {selectedFile && (
            <Card>
              <CardHeader>
                <CardTitle>Split Mode</CardTitle>
                <CardDescription>
                  Choose how you want to split or modify your PDF.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button
                    onClick={() => setSplitMode('extract')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      splitMode === 'extract'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      Extract Pages
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Create a new PDF with only the selected pages
                    </p>
                  </button>

                  <button
                    onClick={() => setSplitMode('remove')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      splitMode === 'remove'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      Remove Pages
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Create a new PDF without the selected pages
                    </p>
                  </button>

                  <button
                    onClick={() => setSplitMode('range')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      splitMode === 'range'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                  >
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      Split by Range
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Split into multiple PDFs using selected pages as breakpoints
                    </p>
                  </button>
                </div>

                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Selected mode:</strong> {getModeDescription()}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Page Selection */}
          {selectedFile && (
            <Card>
              <CardHeader>
                <CardTitle>Select Pages</CardTitle>
                <CardDescription>
                  Click on pages to select them. Selected pages will be highlighted.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <VisualPageManager
                  pdfFile={selectedFile}
                  onPagesChange={handlePagesChange}
                  selectionMode="multiple"
                  showControls={true}
                />
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          {/* Process Button */}
          {selectedFile && (
            <div className="flex justify-center">
              <Button
                onClick={processPDF}
                loading={processing}
                disabled={selectedPages.length === 0}
                size="lg"
                className="min-w-[200px]"
              >
                <Download className="mr-2 h-5 w-5" />
                {getButtonText()}
              </Button>
            </div>
          )}

          {/* Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How it works:
                </h3>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• All processing happens in your browser for maximum privacy</li>
                  <li>• Select pages by clicking on them in the preview grid</li>
                  <li>• Extract mode: Creates a new PDF with only selected pages</li>
                  <li>• Remove mode: Creates a new PDF without selected pages</li>
                  <li>• Range mode: Splits PDF into multiple files at selected breakpoints</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
