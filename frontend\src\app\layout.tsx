import type { Metada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "All PDF Tools - Free PDF Manipulation Suite",
  description: "Comprehensive PDF tools for merging, splitting, converting, compressing, and AI-powered features. Free, secure, and easy to use.",
  keywords: "PDF tools, PDF merge, PDF split, PDF convert, PDF compress, PDF AI, free PDF tools",
  authors: [{ name: "<PERSON><PERSON>", url: "https://github.com/chirag127" }],
  creator: "<PERSON><PERSON>",
  publisher: "<PERSON><PERSON> Singhal",
  robots: "index, follow",
  openGraph: {
    title: "All PDF Tools - Free PDF Manipulation Suite",
    description: "Comprehensive PDF tools for merging, splitting, converting, compressing, and AI-powered features.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "All PDF Tools - Free PDF Manipulation Suite",
    description: "Comprehensive PDF tools for merging, splitting, converting, compressing, and AI-powered features.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
