"use client";

import React, { useState, useEffect } from 'react';
import { PDFDocument, PDFPage } from 'pdf-lib';
import { 
  RotateCw, 
  Trash2, 
  Check, 
  Square, 
  CheckSquare, 
  ArrowUp, 
  ArrowDown,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Button from '@/components/ui/Button';

interface PageInfo {
  pageNumber: number;
  thumbnail: string;
  selected: boolean;
  rotation: number;
}

interface VisualPageManagerProps {
  pdfFile: File;
  onPagesChange?: (selectedPages: number[]) => void;
  onPageRotation?: (pageNumber: number, rotation: number) => void;
  selectionMode?: 'single' | 'multiple' | 'range';
  showControls?: boolean;
  className?: string;
}

export default function VisualPageManager({
  pdfFile,
  onPagesChange,
  onPageRotation,
  selectionMode = 'multiple',
  showControls = true,
  className
}: VisualPageManagerProps) {
  const [pages, setPages] = useState<PageInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfDoc, setPdfDoc] = useState<PDFDocument | null>(null);

  // Load PDF and generate thumbnails
  useEffect(() => {
    loadPDF();
  }, [pdfFile]);

  // Notify parent of page changes
  useEffect(() => {
    const selectedPages = pages
      .filter(page => page.selected)
      .map(page => page.pageNumber);
    onPagesChange?.(selectedPages);
  }, [pages, onPagesChange]);

  const loadPDF = async () => {
    setLoading(true);
    setError(null);

    try {
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdf = await PDFDocument.load(arrayBuffer);
      setPdfDoc(pdf);

      const pageCount = pdf.getPageCount();
      const pageInfos: PageInfo[] = [];

      // Generate thumbnails for each page
      for (let i = 0; i < pageCount; i++) {
        const thumbnail = await generatePageThumbnail(pdf, i);
        pageInfos.push({
          pageNumber: i + 1,
          thumbnail,
          selected: false,
          rotation: 0
        });
      }

      setPages(pageInfos);
    } catch (err) {
      console.error('Error loading PDF:', err);
      setError('Failed to load PDF. Please ensure the file is a valid PDF.');
    } finally {
      setLoading(false);
    }
  };

  const generatePageThumbnail = async (pdf: PDFDocument, pageIndex: number): Promise<string> => {
    try {
      // Create a new PDF with just this page for thumbnail generation
      const singlePagePdf = await PDFDocument.create();
      const [page] = await singlePagePdf.copyPages(pdf, [pageIndex]);
      singlePagePdf.addPage(page);
      
      const pdfBytes = await singlePagePdf.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      // For now, return a placeholder. In a real implementation, you'd use
      // a library like pdf2pic or render the PDF to canvas
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      return '';
    }
  };

  const togglePageSelection = (pageNumber: number) => {
    setPages(prev => prev.map(page => {
      if (page.pageNumber === pageNumber) {
        if (selectionMode === 'single') {
          // Deselect all other pages first
          const updated = prev.map(p => ({ ...p, selected: false }));
          return updated.map(p => 
            p.pageNumber === pageNumber ? { ...p, selected: !page.selected } : p
          );
        } else {
          return { ...page, selected: !page.selected };
        }
      }
      return selectionMode === 'single' ? { ...page, selected: false } : page;
    }));
  };

  const selectAllPages = () => {
    setPages(prev => prev.map(page => ({ ...page, selected: true })));
  };

  const deselectAllPages = () => {
    setPages(prev => prev.map(page => ({ ...page, selected: false })));
  };

  const rotatePage = (pageNumber: number, degrees: number) => {
    setPages(prev => prev.map(page => {
      if (page.pageNumber === pageNumber) {
        const newRotation = (page.rotation + degrees) % 360;
        onPageRotation?.(pageNumber, newRotation);
        return { ...page, rotation: newRotation };
      }
      return page;
    }));
  };

  const movePageUp = (pageNumber: number) => {
    setPages(prev => {
      const currentIndex = prev.findIndex(p => p.pageNumber === pageNumber);
      if (currentIndex > 0) {
        const newPages = [...prev];
        [newPages[currentIndex - 1], newPages[currentIndex]] = 
        [newPages[currentIndex], newPages[currentIndex - 1]];
        return newPages;
      }
      return prev;
    });
  };

  const movePageDown = (pageNumber: number) => {
    setPages(prev => {
      const currentIndex = prev.findIndex(p => p.pageNumber === pageNumber);
      if (currentIndex < prev.length - 1) {
        const newPages = [...prev];
        [newPages[currentIndex], newPages[currentIndex + 1]] = 
        [newPages[currentIndex + 1], newPages[currentIndex]];
        return newPages;
      }
      return prev;
    });
  };

  const selectedCount = pages.filter(page => page.selected).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading PDF pages...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <p className="text-red-800 dark:text-red-200">{error}</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Controls */}
      {showControls && (
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {selectedCount} of {pages.length} pages selected
            </span>
            
            {selectionMode === 'multiple' && (
              <div className="flex space-x-2">
                <Button
                  onClick={selectAllPages}
                  variant="outline"
                  size="sm"
                >
                  Select All
                </Button>
                <Button
                  onClick={deselectAllPages}
                  variant="outline"
                  size="sm"
                >
                  Deselect All
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Page Grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {pages.map((page, index) => (
          <div
            key={page.pageNumber}
            className={cn(
              "relative group border-2 rounded-lg overflow-hidden transition-all duration-200 cursor-pointer",
              page.selected
                ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
            )}
            onClick={() => togglePageSelection(page.pageNumber)}
          >
            {/* Selection Indicator */}
            <div className="absolute top-2 left-2 z-10">
              {page.selected ? (
                <CheckSquare className="h-5 w-5 text-blue-600 dark:text-blue-400 bg-white dark:bg-gray-800 rounded" />
              ) : (
                <Square className="h-5 w-5 text-gray-400 bg-white dark:bg-gray-800 rounded opacity-0 group-hover:opacity-100 transition-opacity" />
              )}
            </div>

            {/* Page Number */}
            <div className="absolute top-2 right-2 z-10 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {page.pageNumber}
            </div>

            {/* Thumbnail */}
            <div 
              className="aspect-[3/4] bg-white dark:bg-gray-700 flex items-center justify-center"
              style={{ transform: `rotate(${page.rotation}deg)` }}
            >
              {page.thumbnail ? (
                <iframe
                  src={page.thumbnail}
                  className="w-full h-full border-0"
                  title={`Page ${page.pageNumber}`}
                />
              ) : (
                <div className="text-gray-400 text-center">
                  <Eye className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-xs">Page {page.pageNumber}</p>
                </div>
              )}
            </div>

            {/* Page Controls */}
            {showControls && (
              <div className="absolute bottom-0 left-0 right-0 bg-black/70 p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex justify-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      rotatePage(page.pageNumber, 90);
                    }}
                    className="p-1 text-white hover:bg-white/20 rounded"
                    title="Rotate 90°"
                  >
                    <RotateCw className="h-3 w-3" />
                  </button>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      movePageUp(page.pageNumber);
                    }}
                    disabled={index === 0}
                    className="p-1 text-white hover:bg-white/20 rounded disabled:opacity-50"
                    title="Move up"
                  >
                    <ArrowUp className="h-3 w-3" />
                  </button>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      movePageDown(page.pageNumber);
                    }}
                    disabled={index === pages.length - 1}
                    className="p-1 text-white hover:bg-white/20 rounded disabled:opacity-50"
                    title="Move down"
                  >
                    <ArrowDown className="h-3 w-3" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
