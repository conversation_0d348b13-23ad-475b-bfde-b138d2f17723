

# Masterplan for All PDF Tools

**Document Version:** 1.0
**Owner:** <PERSON><PERSON>
**Status:** final
**Prepared for:** augment code assistant
**Prepared by:** <PERSON><PERSON>

---

## Project Overview
This document outlines the masterplan for "All PDF Tools," a comprehensive Next.js web application designed to provide a complete suite of PDF manipulation tools. The application will function as a free, all-in-one PDF utility that allows users to perform various operations on PDF files directly from their web browser, with a focus on mobile accessibility. It leverages client-side processing for most standard operations to ensure speed and minimize server costs, while utilizing a Python FastAPI backend for heavy-lifting and advanced AI features powered by the user's own Gemini API key. No user authentication or cloud storage is required, prioritizing user privacy and ease of use.

## Project Goals
- To deliver a high-performance, user-friendly, and free suite of essential PDF tools.
- To prioritize client-side processing for speed and to maintain a minimal-cost backend infrastructure.
- To provide powerful AI-driven features (Chat with PDF, Summarization) via the Gemini API, empowering users with advanced document analysis capabilities.
- To ensure the application is intuitive and accessible, especially for mobile users.
- To create a secure application where user files are processed in-memory or locally, and sensitive data like API keys are handled responsibly.

## Technical Stack
- **Frontend**: Next.js (latest stable), React, Tailwind CSS, pdf-lib.js (for client-side PDF manipulation)
- **Backend**: Python 3.10+, FastAPI, PyMuPDF (for text extraction), Uvicorn
- **Database**: None (Stateless Design)
- **Deployment**:
    - **Frontend**: Vercel or Netlify
    - **Backend**: Render.com (Free Tier)

## Project Scope
### In Scope
- **(High Priority) Document Organization:** Merge multiple PDFs, Split PDFs into multiple documents, Remove specific pages, and Extract selected pages into a new PDF.
- **(High Priority) Conversion Tools:** Convert JPG/PNG/WEBP to PDF and convert PDF to JPG/PNG. All other formats are out of scope for the initial build.
- **(High Priority) Editing Tools:** Rotate pages within a PDF.
- **(High Priority) Security Tools:** Unlock a password-protected PDF (when the user provides the password), and Protect a PDF with a new password.
- **(High Priority) Enhancement Tools:** Compress a PDF to reduce its file size.
- **(High Priority) AI Features:** AI-powered Chat with PDF (using RAG) and AI PDF Summarizer.
- **(High Priority) Settings:** A dedicated page or section for the user to input and manage their Gemini API Key and select the desired Gemini model.

### Out of Scope
- All "Medium" and "Low" priority features listed in the raw idea will be considered for "Future Enhancements" (see section below).
- User accounts, authentication, or user-specific cloud storage.
- Processing of any file formats for conversion other than those explicitly listed in the "In Scope" section.
- Advanced PDF editing features like text/image modification, redacting, or signing.

## Functional Requirements

### Feature Area 1: Document Organization (Client-Side)
- **FR1.1: Merge PDF:** Users shall be able to upload multiple PDF files, reorder them via a drag-and-drop interface, and merge them into a single PDF document.
- **FR1.2: Split PDF:** Users shall upload a PDF and select specific pages or page ranges to split into a new PDF document. The interface will be a visual grid of page thumbnails.
- **FR1.3: Remove Pages:** Users shall upload a PDF and select specific pages via a visual grid interface to remove from the document.
- **FR1.4: Extract Pages:** This will be functionally the same as Split PDF, allowing users to select and save specific pages as a new file.

### Feature Area 2: Conversion Tools (Client & Server-Side)
- **FR2.1: Image to PDF (Client-Side):** Users shall be able to upload one or more images (JPG, PNG, WEBP) and convert them into a single PDF file.
- **FR2.2: PDF to Image (Client-Side):** Users shall upload a PDF and convert its pages into high-quality JPG or PNG images.

### Feature Area 3: Editing & Security
- **FR3.1: Rotate PDF (Client-Side):** Users shall upload a PDF and rotate selected pages by 90°, 180°, or 270°.
- **FR3.2: Protect PDF (Backend):** Users shall upload a PDF and add password protection. The user will be prompted to enter and confirm the desired password.
- **FR3.3: Unlock PDF (Backend):** Users shall upload a password-protected PDF. They will be prompted to enter the correct password to remove the protection. A disclaimer will be shown: "I confirm that I have the legal right to access and modify this document."

### Feature Area 4: AI & Enhancement Tools (Backend)
- **FR4.1: Compress PDF (Backend):** Users shall upload a PDF and receive a compressed version with reduced file size.
- **FR4.2: Chat with PDF (Backend):** Users shall upload a PDF. The backend will process and index the document. The user can then ask questions in a chat interface, and the AI will provide answers based on the document's content.
- **FR4.3: Summarize PDF (Backend):** Users shall upload a PDF, and the backend will use the Gemini API to generate a concise summary.
- **FR4.4: Settings & API Key:** A settings page will allow users to securely input their Gemini API key, which will be stored in the browser's `localStorage` with a clear security disclaimer. Users can also select which available Gemini model to use for the AI features.

## Non-Functional Requirements
- **Performance:** Client-side operations should be responsive for files up to 25MB. For larger files, a warning about potential slowdowns will be displayed. Server-side operations will include progress indicators.
- **Scalability:** The architecture will be stateless. The frontend will be deployed on a serverless platform (Vercel/Netlify), and the backend will run on a Render.com free-tier server, designed to handle one request at a time efficiently.
- **Security:** User files will be processed in-memory and deleted immediately after processing. User-provided API keys will not be transmitted to our server for non-AI tasks and will be stored only in the user's browser `localStorage`.
- **Usability:** The UI will be minimalist, modern, and mobile-first, ensuring an intuitive user experience.

## Implementation Plan

### Phase 1: Setup & Foundation (Frontend & Backend)
- **Task 1: Initialize Next.js Project:** Set up a new Next.js project using `create-next-app`. Configure with TypeScript and Tailwind CSS.
- **Task 2: Initialize FastAPI Project:** Set up a new Python project with a virtual environment. Install FastAPI, Uvicorn, and other dependencies.
- **Task 3: Basic UI Layout:** Create the main application layout, including a header, a central content area for the tools, and a footer. Design a responsive grid to display the available PDF tools.
- **Task 4: File Handling Component:** Create a reusable React component for file selection (drag-and-drop and file input). This component will handle file validation (type and size).
- **Task 5: Project Structure:** Organize both frontend and backend directories as detailed in the "Project Structure" section below.

### Phase 2: Core Client-Side PDF Functionality
- **Task 1: Install `pdf-lib.js`:** Add the `pdf-lib` library to the Next.js project.
- **Task 2: Implement Visual Page Management UI:** Create a React component that takes a PDF file as input, renders each page as a selectable thumbnail in a grid, and allows for drag-and-drop reordering, selection, rotation, and deletion.
- **Task 3: Implement Merge PDF:** Develop the logic to merge multiple `PdfLib.PDFDocument` instances into one.
- **Task 4: Implement Split/Extract/Remove Pages:** Develop the logic to create new documents or modify existing ones based on user page selections in the visual UI.
- **Task 5: Implement Rotate Pages:** Add controls to the visual UI to rotate pages and implement the corresponding `pdf-lib` logic.
- **Task 6: Implement Image-to-PDF & PDF-to-Image:** Implement the client-side conversion tools.

### Phase 3: Backend Setup and AI Feature Implementation
- **Task 1: Dockerize FastAPI:** Create a `Dockerfile` for the FastAPI application to ensure easy deployment on Render.
- **Task 2: Implement Backend API for RAG (Chat with PDF):**
    - Create a `/api/chat` endpoint.
    - Use `PyMuPDF` to extract text from the uploaded PDF.
    - Implement a text chunking strategy.
    - Use the Gemini API to generate embeddings for each chunk.
    - Implement the logic to handle user queries by finding relevant chunks and passing them as context to the Gemini completion model.
- **Task 3: Implement AI Summarizer:** Create a `/api/summarize` endpoint that takes the PDF text, chunks it if necessary, and uses a tailored prompt to ask Gemini to summarize it.
- **Task 4: Implement AI Feature UI:**
    - Create the chat interface for "Chat with PDF".
    - Create the UI for uploading a file and displaying the generated summary.
- **Task 5: Implement Settings Page:**
    - Create the UI for the user to input their Gemini API Key. Store the key in `localStorage` with a disclaimer.
    - Implement the logic to dynamically fetch the list of available Gemini models from the backend.
    - Allow users to select a model from a dropdown, which will be saved in `localStorage`.

### Phase 4: Server-Side PDF Utilities
- **Task 1: Implement PDF Protection:** Create a `/api/protect` endpoint that takes a PDF and a password and adds encryption.
- **Task 2: Implement PDF Unlock:** Create a `/api/unlock` endpoint that takes a PDF and a password and removes encryption.
- **Task 3: Implement PDF Compression:** Create a `/api/compress` endpoint that uses a Python library (e.g., `ghostscript` if available on Render) to reduce PDF file size.
- **Task 4: Integrate Frontend:** Connect the frontend UI to these new backend endpoints, ensuring progress indicators and error handling are in place.

### Phase 5: Testing, Deployment & Documentation
- **Task 1: Unit & Integration Testing:** Write unit tests for critical backend logic (e.g., RAG context retrieval) and frontend components.
- **Task 2: End-to-End Testing:** Manually test all user flows, focusing on file upload, processing, and download, on both desktop and mobile browsers.
- **Task 3: Finalize UI/UX:** Polish the UI, add hover effects, transitions, and ensure accessibility (WCAG).
- **Task 4: Deployment:**
    - Deploy the Next.js frontend to Vercel.
    - Deploy the FastAPI backend to Render.com using the Dockerfile.
- **Task 5: Documentation:** Create a comprehensive `README.md` and a `CHANGELOG.md`.

## API Endpoints (if applicable)
- `POST /api/chat` - Body: `{ file: File, query: string, history: Array, model: string }`. Handles a user's message in the Chat with PDF session.
- `POST /api/summarize` - Body: `{ file: File, model: string }`. Generates a summary of the PDF.
- `POST /api/compress` - Body: `{ file: File }`. Compresses the PDF.
- `POST /api/protect` - Body: `{ file: File, password: string }`. Adds a password to the PDF.
- `POST /api/unlock` - Body: `{ file: File, password: string }`. Removes a password from the PDF.
- `GET /api/list-models` - Retrieves the list of available Gemini models.

## Data Models (if applicable)
### GeminiModelList
- `models`: `Array<Object>` - list of model details
    - `name`: `string`
    - `description`: `string`
    - `input_token_limit`: `number`
    - `output_token_limit`: `number`

### ChatRequest
- `file`: The PDF file being uploaded.
- `query`: `string` - The user's current question.
- `history`: `Array` - The previous conversation turns.
- `model`: `string` - The selected Gemini model name.

## Project Structure
```
project-root/
├── frontend/
│   ├── app/
│   │   ├── (tools)/              # Directory for tool-specific pages
│   │   │   ├── merge/page.tsx
│   │   │   └── chat/page.tsx
│   │   ├── settings/page.tsx
│   │   └── layout.tsx
│   ├── components/
│   │   ├── ui/                   # Shared UI components (Button, Input, etc.)
│   │   ├── FileUploader.tsx
│   │   └── VisualPageManager.tsx
│   ├── lib/
│   │   ├── api.ts                # Centralized API call functions
│   │   └── utils.ts
│   ├── public/
│   ├── tailwind.config.ts
│   └── next.config.js
├── backend/
│   ├── app/
│   │   ├── main.py               # FastAPI app initialization
│   │   ├── routers/              # API endpoint routers
│   │   │   ├── ai_tools.py
│   │   │   └── pdf_utils.py
│   │   ├── core/
│   │   │   └── RAG.py            # RAG logic implementation
│   │   └── models.py             # Pydantic models
│   ├── requirements.txt
│   └── Dockerfile
└── README.md
```

## Environment Variables
```
# Required environment variables (Frontend - managed by user in-app)
# These will be entered in the settings page and stored in localStorage.
NEXT_PUBLIC_GEMINI_API_KEY=The user's Gemini API Key.

# Required environment variables (Backend - for fetching model list)
# This will be a restricted key stored securely on the Render.com server.
GOOGLE_API_KEY=A Google AI Platform API key for server-side operations like fetching model lists.
```

## Testing Strategy
- **Unit Tests:** Jest will be used for testing individual React components and utility functions in the frontend. `pytest` will be used for unit testing Python functions in the backend (e.g., text extraction, chunking logic).
- **Integration Tests:** Test the communication between the frontend and backend API endpoints.
- **End-to-End (E2E) Tests:** Manual testing will be conducted to verify complete user workflows, from file upload to downloading the final processed file.

## Deployment Strategy
- The `frontend` will be deployed on Vercel, linked to the main branch of the repository for continuous deployment.
- The `backend` will be containerized using Docker and deployed on Render.com's free tier. Render will build and deploy the `Dockerfile` from the `backend` directory.

## Maintenance Plan
- **Dependencies:** Regularly update npm and pip packages to their latest stable versions to incorporate security patches and improvements.
- **Monitoring:** Monitor logs on Vercel and Render for any runtime errors.
- **Backlog Grooming:** Review the "Future Enhancements" section periodically to plan for the next development cycle.

## Risks and Mitigations
| Risk | Impact | Likelihood | Mitigation |
|---|---|---|---|
| Browser Performance Issues | Medium | Medium | Implement client-side processing for files under 25MB and display a warning for larger files, guiding users to more stable, server-based tools if necessary. |
| Gemini API Costs | High | High | The user provides their own API key, transferring the cost and responsibility to them. This will be made clear in the UI. |
| Inconsistencies in PDF Rendering | Medium | Medium | Use a robust and well-maintained library like `pdf-lib.js` for client-side work and `PyMuPDF` for backend work to ensure high fidelity. |
| Render Free Tier Limitations | Medium | High | Design the backend to be stateless and efficient. Implement aggressive client-side processing to minimize server load. AI features will be used one at a time. |

## Future Enhancements
- **Medium Priority Features:** Organize Pages (advanced visual editor), Add Page Numbers, Add Watermark, Crop PDF, Repair PDF, OCR PDF, Scan to PDF (via device camera API), Sign PDF, and AI-powered PDF Translation.
- **Low Priority Features:** Basic Text/Image Editing, Redact PDF, Compare PDF, and AI Question Generator.
- **Multi-language Support (i18n):** Externalize all UI strings into resource files to allow for future localization.
- **Expanded File Conversion:** Add support for all other formats listed in the original document (Word, PowerPoint, Excel, etc.), which will require significant backend processing.

## Development Guidelines
### Code Quality & Design Principles
- Apply SOLID, DRY (via abstraction), and KISS principles.
- Design modular, reusable components/functions with comprehensive error handling.
- Add concise, useful function-level comments and optimize for readability.
- All credentials/configs must be handled exclusively via environment variables or user input as specified.
- A `.env.example` file will be provided for the backend.
- Centralize all API endpoint URLs in a single `lib/api.ts` file on the frontend.

### Asset Generation
- Any necessary icons or simple graphics will be created as SVGs and used directly. Build scripts will not be necessary for this project's scope.

### Documentation Requirements
- A comprehensive `README.md` will be created with an overview, setup instructions, and deployment guide.
- A `CHANGELOG.md` will be maintained.

## Tool Usage Instructions

### Specific Instructions for Gemini Model Listing
The backend `list-models` endpoint will be implemented in Python as follows. This code should be placed in `backend/app/routers/ai_tools.py`.

```python
import google.generativeai as genai
import os
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

# Configure with an API key stored as an environment variable on the server
genai.configure(api_key=os.environ.get("GOOGLE_API_KEY"))

class ModelInfo(BaseModel):
    name: str
    description: str
    input_token_limit: int
    output_token_limit: int

@router.get("/list-models", response_model=List[ModelInfo])
async def list_available_models():
    """
    Lists all available Gemini models from the Generative AI service.
    """
    try:
        models_list = []
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                models_list.append(ModelInfo(
                    name=model.name,
                    description=getattr(model, 'description', 'N/A'),
                    input_token_limit=getattr(model, 'input_token_limit', 0),
                    output_token_limit=getattr(model, 'output_token_limit', 0)
                ))
        return models_list
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

```

### General Tool Usage
- Use the **context7 MCP server** to gather context about libraries and frameworks.
- Use the **clear thought MCP servers** for problem-solving, architectural design, and debugging.
- Use the **date and time MCP server** (`getCurrentDateTime_node`) to add a "last updated" timestamp to the README.md.
- Use the **websearch tool** for external information.
- Follow system and environment considerations, including using semicolon (`;`) for PowerShell commands and using native path manipulation libraries.

## Conclusion
This masterplan provides a complete and detailed blueprint for developing the "All PDF Tools" application. By prioritizing features, choosing a smart technical architecture that balances client and server-side processing, and clearly defining the project's scope, we have a solid foundation for building a successful and valuable product. The implementation plan is phased to deliver core value quickly while building towards the full feature set.