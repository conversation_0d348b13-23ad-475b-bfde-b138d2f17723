# All PDF Tools - Free PDF Manipulation Suite

A comprehensive, free-to-use PDF manipulation suite built with Next.js and FastAPI. Perform various PDF operations directly in your browser with client-side processing for maximum privacy and speed, plus AI-powered features using Google's Gemini API.

## 🚀 Features

### Document Organization

-   **Merge PDF**: Combine multiple PDF files into one document
-   **Split PDF**: Split PDF into multiple documents or extract specific pages
-   **Remove Pages**: Delete specific pages from PDF documents
-   **Extract Pages**: Save selected pages as a new PDF file

### Conversion Tools

-   **Image to PDF**: Convert JPG, PNG, WEBP images to PDF
-   **PDF to Image**: Convert PDF pages to high-quality JPG/PNG images

### Security & Optimization

-   **Protect PDF**: Add password protection to PDF files
-   **Unlock PDF**: Remove password protection (with legal disclaimer)
-   **Compress PDF**: Reduce file size while maintaining quality

### AI-Powered Features

-   **Chat with PDF**: Ask questions about your PDF content using RAG (Retrieval Augmented Generation)
-   **Summarize PDF**: Generate AI-powered summaries of your documents

## 🛠 Tech Stack

### Frontend

-   **Next.js 14** - React framework with App Router
-   **TypeScript** - Type-safe development
-   **Tailwind CSS** - Utility-first CSS framework
-   **pdf-lib.js** - Client-side PDF manipulation
-   **react-dropzone** - File upload with drag-and-drop
-   **Lucide React** - Beautiful icons

### Backend

-   **Python 3.11+** - Programming language
-   **FastAPI** - Modern, fast web framework
-   **PyMuPDF** - PDF processing and text extraction
-   **Google Generative AI** - Gemini API integration
-   **Uvicorn** - ASGI server

## 📋 Prerequisites

-   **Node.js** 18.0 or higher
-   **Python** 3.11 or higher
-   **npm** or **yarn** package manager
-   **Google Gemini API Key** (for AI features)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/chirag127/All-PDF-Tools-Latest.git
cd All-PDF-Tools-Latest
```

### 2. Frontend Setup

```bash
cd frontend
npm install
npm run dev
```

The frontend will be available at `http://localhost:3000`

### 3. Backend Setup

```bash
cd backend
python -m venv venv

# On Windows
venv\Scripts\activate

# On macOS/Linux
source venv/bin/activate

pip install -r requirements.txt
```

### 4. Environment Configuration

Create a `.env` file in the backend directory:

```bash
cp .env.example .env
```

Edit the `.env` file and add your configuration:

```env
# Google AI Platform API key for server-side operations
GOOGLE_API_KEY=your_google_api_key_here

# Server configuration
PORT=8000
HOST=0.0.0.0
ENVIRONMENT=development

# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://localhost:3000
```

### 5. Start the Backend

```bash
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The backend API will be available at `http://localhost:8000`

## 🔧 Configuration

### Gemini API Key Setup

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. In the application, go to Settings and enter your API key
4. The key is stored locally in your browser for privacy

### Environment Variables

#### Frontend (.env.local)

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### Backend (.env)

```env
GOOGLE_API_KEY=your_google_api_key_here
PORT=8000
HOST=0.0.0.0
ENVIRONMENT=development
CORS_ORIGINS=http://localhost:3000,https://localhost:3000
```

## 📁 Project Structure

```
All-PDF-Tools-Latest/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   │   ├── (tools)/     # Tool-specific pages
│   │   │   ├── settings/    # Settings page
│   │   │   ├── layout.tsx   # Root layout
│   │   │   └── page.tsx     # Home page
│   │   ├── components/      # React components
│   │   │   ├── ui/          # Reusable UI components
│   │   │   └── FileUploader.tsx
│   │   └── lib/             # Utilities and API
│   │       ├── api.ts       # API client
│   │       └── utils.ts     # Utility functions
│   ├── package.json
│   └── tailwind.config.ts
├── backend/                 # FastAPI backend application
│   ├── app/
│   │   ├── routers/         # API route handlers
│   │   │   ├── ai_tools.py  # AI-powered endpoints
│   │   │   └── pdf_utils.py # PDF utility endpoints
│   │   ├── core/            # Core functionality
│   │   │   └── rag.py       # RAG implementation
│   │   ├── main.py          # FastAPI app
│   │   └── models.py        # Pydantic models
│   ├── requirements.txt
│   ├── Dockerfile
│   └── .env.example
└── README.md
```

## 🔒 Privacy & Security

-   **Client-Side Processing**: Most PDF operations happen in your browser
-   **Local Storage**: API keys are stored locally, never on our servers
-   **No File Storage**: Files are processed in-memory and immediately deleted
-   **HTTPS Ready**: Production deployment supports HTTPS
-   **CORS Protection**: Configured for secure cross-origin requests

## 🚀 Deployment

### Frontend (Vercel)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Backend (Render.com)

1. Connect your GitHub repository to Render
2. Create a new Web Service
3. Set environment variables in Render dashboard
4. Deploy using the included Dockerfile

## 📖 API Documentation

Once the backend is running, visit:

-   **Swagger UI**: `http://localhost:8000/docs`
-   **ReDoc**: `http://localhost:8000/redoc`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Chirag Singhal** ([@chirag127](https://github.com/chirag127))

## 🙏 Acknowledgments

-   [Next.js](https://nextjs.org/) - React framework
-   [FastAPI](https://fastapi.tiangolo.com/) - Python web framework
-   [pdf-lib](https://pdf-lib.js.org/) - PDF manipulation library
-   [PyMuPDF](https://pymupdf.readthedocs.io/) - Python PDF library
-   [Google Gemini](https://ai.google.dev/) - AI capabilities
-   [Tailwind CSS](https://tailwindcss.com/) - CSS framework
-   [Lucide](https://lucide.dev/) - Icon library

---

**Last Updated**: 2025-07-11T17:18:40.679Z

For support or questions, please open an issue on GitHub.
