/**
 * API Configuration and Utility Functions
 * Centralized API endpoint management for All PDF Tools
 */

// API Base URL - will be configured based on environment
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// API Endpoints
export const API_ENDPOINTS = {
  // AI Tools
  LIST_MODELS: `${API_BASE_URL}/api/list-models`,
  CHAT_PDF: `${API_BASE_URL}/api/chat`,
  SUMMARIZE_PDF: `${API_BASE_URL}/api/summarize`,
  
  // PDF Utilities
  COMPRESS_PDF: `${API_BASE_URL}/api/compress`,
  PROTECT_PDF: `${API_BASE_URL}/api/protect`,
  UNLOCK_PDF: `${API_BASE_URL}/api/unlock`,
  
  // Health Check
  HEALTH: `${API_BASE_URL}/health`,
} as const;

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ModelInfo {
  name: string;
  description: string;
  input_token_limit: number;
  output_token_limit: number;
}

export interface ChatResponse {
  response: string;
  sources: string[];
}

export interface SummarizeResponse {
  summary: string;
  word_count: number;
  summary_word_count: number;
}

// API Utility Functions
export class ApiClient {
  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || errorData.detail || `HTTP ${response.status}`);
    }
    return response.json();
  }

  static async get<T>(url: string): Promise<T> {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    return this.handleResponse<T>(response);
  }

  static async post<T>(url: string, data: any): Promise<T> {
    const response = await fetch(url, {
      method: 'POST',
      body: data instanceof FormData ? data : JSON.stringify(data),
      headers: data instanceof FormData ? {} : {
        'Content-Type': 'application/json',
      },
    });
    return this.handleResponse<T>(response);
  }

  static async postFile<T>(url: string, formData: FormData): Promise<T> {
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });
    return this.handleResponse<T>(response);
  }

  static async downloadFile(url: string, formData: FormData): Promise<Blob> {
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || errorData.detail || `HTTP ${response.status}`);
    }
    
    return response.blob();
  }
}

// Specific API Functions
export const apiService = {
  // Get available Gemini models
  async getModels(): Promise<ModelInfo[]> {
    return ApiClient.get<ModelInfo[]>(API_ENDPOINTS.LIST_MODELS);
  },

  // Chat with PDF
  async chatWithPdf(file: File, query: string, history: any[], model: string, apiKey: string): Promise<ChatResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('query', query);
    formData.append('history', JSON.stringify(history));
    formData.append('model', model);
    formData.append('api_key', apiKey);
    
    return ApiClient.postFile<ChatResponse>(API_ENDPOINTS.CHAT_PDF, formData);
  },

  // Summarize PDF
  async summarizePdf(file: File, model: string, summaryType: string, apiKey: string): Promise<SummarizeResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('model', model);
    formData.append('summary_type', summaryType);
    formData.append('api_key', apiKey);
    
    return ApiClient.postFile<SummarizeResponse>(API_ENDPOINTS.SUMMARIZE_PDF, formData);
  },

  // Compress PDF
  async compressPdf(file: File): Promise<Blob> {
    const formData = new FormData();
    formData.append('file', file);
    
    return ApiClient.downloadFile(API_ENDPOINTS.COMPRESS_PDF, formData);
  },

  // Protect PDF
  async protectPdf(file: File, password: string): Promise<Blob> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('password', password);
    
    return ApiClient.downloadFile(API_ENDPOINTS.PROTECT_PDF, formData);
  },

  // Unlock PDF
  async unlockPdf(file: File, password: string): Promise<Blob> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('password', password);
    
    return ApiClient.downloadFile(API_ENDPOINTS.UNLOCK_PDF, formData);
  },

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return ApiClient.get(API_ENDPOINTS.HEALTH);
  },
};

// File download utility
export const downloadBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};
