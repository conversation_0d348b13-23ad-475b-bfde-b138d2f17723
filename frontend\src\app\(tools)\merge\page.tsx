"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Download, Merge, FileText, ArrowUpDown } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FileUploader from '@/components/FileUploader';
import { downloadBlob, generateFilename } from '@/lib/utils';
import { PDFDocument } from 'pdf-lib';

export default function MergePDFPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (files: File[]) => {
    setSelectedFiles(files);
    setError(null);
  };

  const handleFileRemove = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
  };

  const moveFile = (fromIndex: number, toIndex: number) => {
    const newFiles = [...selectedFiles];
    const [movedFile] = newFiles.splice(fromIndex, 1);
    newFiles.splice(toIndex, 0, movedFile);
    setSelectedFiles(newFiles);
  };

  const mergePDFs = async () => {
    if (selectedFiles.length < 2) {
      setError('Please select at least 2 PDF files to merge.');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      // Create a new PDF document
      const mergedPdf = await PDFDocument.create();

      // Process each file
      for (const file of selectedFiles) {
        const arrayBuffer = await file.arrayBuffer();
        const pdf = await PDFDocument.load(arrayBuffer);
        const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        
        pages.forEach((page) => {
          mergedPdf.addPage(page);
        });
      }

      // Generate the merged PDF
      const pdfBytes = await mergedPdf.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      // Generate filename
      const firstFileName = selectedFiles[0].name;
      const mergedFileName = generateFilename(firstFileName, 'merged');
      
      // Download the file
      downloadBlob(blob, mergedFileName);

    } catch (err) {
      console.error('Error merging PDFs:', err);
      setError('Failed to merge PDF files. Please ensure all files are valid PDFs.');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Tools</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Merge className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Merge PDF Files
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Combine multiple PDF files into a single document. Files will be merged in the order shown below.
          </p>
        </div>

        <div className="space-y-6">
          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Select PDF Files</CardTitle>
              <CardDescription>
                Choose multiple PDF files to merge. You can reorder them by dragging.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploader
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                acceptedFileTypes="PDF"
                multiple={true}
                maxFiles={10}
                selectedFiles={selectedFiles}
              />
            </CardContent>
          </Card>

          {/* File Order Management */}
          {selectedFiles.length > 1 && (
            <Card>
              <CardHeader>
                <CardTitle>File Order</CardTitle>
                <CardDescription>
                  Drag files to reorder them. The merged PDF will follow this order.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={`${file.name}-${index}`}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex flex-col space-y-1">
                          <button
                            onClick={() => index > 0 && moveFile(index, index - 1)}
                            disabled={index === 0}
                            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Move up"
                          >
                            <ArrowUpDown className="h-3 w-3 rotate-180" />
                          </button>
                          <button
                            onClick={() => index < selectedFiles.length - 1 && moveFile(index, index + 1)}
                            disabled={index === selectedFiles.length - 1}
                            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Move down"
                          >
                            <ArrowUpDown className="h-3 w-3" />
                          </button>
                        </div>
                        
                        <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded">
                          <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {index + 1}. {file.name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleFileRemove(index)}
                        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                        title="Remove file"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          {/* Merge Button */}
          <div className="flex justify-center">
            <Button
              onClick={mergePDFs}
              loading={processing}
              disabled={selectedFiles.length < 2}
              size="lg"
              className="min-w-[200px]"
            >
              <Download className="mr-2 h-5 w-5" />
              {processing ? 'Merging PDFs...' : 'Merge & Download'}
            </Button>
          </div>

          {/* Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              How it works:
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• All processing happens in your browser for maximum privacy</li>
              <li>• Files are merged in the order shown above</li>
              <li>• The merged PDF will be automatically downloaded</li>
              <li>• No file size limits, but larger files may take longer to process</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
