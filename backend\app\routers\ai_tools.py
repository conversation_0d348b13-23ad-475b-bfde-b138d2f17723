"""
AI Tools Router - Handles AI-powered PDF operations
"""

import google.generativeai as genai
import os
import fitz  # PyMuPDF
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import List
import tempfile
import logging

from app.models import (
    ModelInfo, ChatRequest, ChatResponse, SummarizeRequest, 
    SummarizeResponse, ErrorResponse
)
from app.core.rag import RAGProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# Configure Gemini API with server key for model listing
server_api_key = os.getenv("GOOGLE_API_KEY")
if server_api_key:
    genai.configure(api_key=server_api_key)

@router.get("/list-models", response_model=List[ModelInfo])
async def list_available_models():
    """
    Lists all available Gemini models from the Generative AI service.
    """
    try:
        if not server_api_key:
            raise HTTPException(
                status_code=500, 
                detail="Server API key not configured"
            )
        
        models_list = []
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                models_list.append(ModelInfo(
                    name=model.name,
                    description=getattr(model, 'description', 'N/A'),
                    input_token_limit=getattr(model, 'input_token_limit', 0),
                    output_token_limit=getattr(model, 'output_token_limit', 0)
                ))
        return models_list
    except Exception as e:
        logger.error(f"Error listing models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/chat", response_model=ChatResponse)
async def chat_with_pdf(
    file: UploadFile = File(...),
    query: str = Form(...),
    history: str = Form("[]"),  # JSON string of chat history
    model: str = Form("gemini-pro"),
    api_key: str = Form(...)
):
    """
    Chat with PDF using RAG (Retrieval Augmented Generation)
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are supported"
            )
        
        # Configure Gemini with user's API key
        genai.configure(api_key=api_key)
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Initialize RAG processor
            rag_processor = RAGProcessor(api_key=api_key, model_name=model)
            
            # Process PDF and get response
            response = await rag_processor.process_query(temp_file_path, query)
            
            return ChatResponse(
                response=response["answer"],
                sources=response.get("sources", [])
            )
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"Error in chat with PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/summarize", response_model=SummarizeResponse)
async def summarize_pdf(
    file: UploadFile = File(...),
    model: str = Form("gemini-pro"),
    summary_type: str = Form("comprehensive"),
    api_key: str = Form(...)
):
    """
    Generate a summary of the PDF content
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are supported"
            )
        
        # Configure Gemini with user's API key
        genai.configure(api_key=api_key)
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Extract text from PDF
            doc = fitz.open(temp_file_path)
            text_content = ""
            for page in doc:
                text_content += page.get_text()
            doc.close()
            
            if not text_content.strip():
                raise HTTPException(
                    status_code=400,
                    detail="No text content found in PDF"
                )
            
            # Prepare prompt based on summary type
            if summary_type == "brief":
                prompt = f"""Please provide a brief summary (2-3 paragraphs) of the following document:

{text_content[:8000]}  # Limit text to avoid token limits

Focus on the main points and key takeaways."""
            else:  # comprehensive
                prompt = f"""Please provide a comprehensive summary of the following document:

{text_content[:8000]}  # Limit text to avoid token limits

Include:
1. Main topic and purpose
2. Key points and arguments
3. Important details and findings
4. Conclusions or recommendations"""
            
            # Generate summary using Gemini
            model_instance = genai.GenerativeModel(model)
            response = model_instance.generate_content(prompt)
            
            # Count words
            original_word_count = len(text_content.split())
            summary_word_count = len(response.text.split())
            
            return SummarizeResponse(
                summary=response.text,
                word_count=original_word_count,
                summary_word_count=summary_word_count
            )
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"Error in PDF summarization: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
