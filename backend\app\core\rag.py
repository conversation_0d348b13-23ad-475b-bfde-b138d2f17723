"""
RAG (Retrieval Augmented Generation) processor for Chat with PDF functionality
"""

import fitz  # PyMuPDF
import google.generativeai as genai
import logging
from typing import List, Dict, Any
import re
import math

logger = logging.getLogger(__name__)

class RAGProcessor:
    """Handles RAG processing for PDF chat functionality"""
    
    def __init__(self, api_key: str, model_name: str = "gemini-pro"):
        """
        Initialize RAG processor
        
        Args:
            api_key: Gemini API key
            model_name: Name of the Gemini model to use
        """
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text content from PDF file
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text content
        """
        try:
            doc = fitz.open(pdf_path)
            text_content = ""
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                page_text = page.get_text()
                # Add page number for reference
                text_content += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            
            doc.close()
            return text_content
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {str(e)}")
            raise
    
    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """
        Split text into overlapping chunks for better context retrieval
        
        Args:
            text: Text to chunk
            chunk_size: Maximum size of each chunk
            overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks with metadata
        """
        # Clean and normalize text
        text = re.sub(r'\s+', ' ', text.strip())
        
        chunks = []
        start = 0
        chunk_id = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence boundaries
            if end < len(text):
                # Look for sentence endings within the last 200 characters
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start + chunk_size - 200:
                    end = sentence_end + 1
                else:
                    # Look for word boundaries
                    word_end = text.rfind(' ', start, end)
                    if word_end > start:
                        end = word_end
            
            chunk_text = text[start:end].strip()
            
            if chunk_text:
                # Extract page number if available
                page_match = re.search(r'--- Page (\d+) ---', chunk_text)
                page_num = int(page_match.group(1)) if page_match else None
                
                chunks.append({
                    'id': chunk_id,
                    'text': chunk_text,
                    'start_pos': start,
                    'end_pos': end,
                    'page': page_num
                })
                chunk_id += 1
            
            # Move start position with overlap
            start = max(start + 1, end - overlap)
            
            # Prevent infinite loop
            if start >= len(text):
                break
        
        return chunks
    
    def find_relevant_chunks(self, chunks: List[Dict[str, Any]], query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Find the most relevant chunks for a given query using simple keyword matching
        In a production system, this would use embeddings and vector similarity
        
        Args:
            chunks: List of text chunks
            query: User query
            top_k: Number of top chunks to return
            
        Returns:
            Most relevant chunks
        """
        query_words = set(query.lower().split())
        
        # Score chunks based on keyword overlap
        scored_chunks = []
        for chunk in chunks:
            chunk_words = set(chunk['text'].lower().split())
            overlap = len(query_words.intersection(chunk_words))
            score = overlap / len(query_words) if query_words else 0
            
            scored_chunks.append({
                **chunk,
                'relevance_score': score
            })
        
        # Sort by relevance score and return top_k
        scored_chunks.sort(key=lambda x: x['relevance_score'], reverse=True)
        return scored_chunks[:top_k]
    
    async def process_query(self, pdf_path: str, query: str) -> Dict[str, Any]:
        """
        Process a user query against a PDF document using RAG
        
        Args:
            pdf_path: Path to the PDF file
            query: User's question
            
        Returns:
            Dictionary containing the answer and source information
        """
        try:
            # Extract text from PDF
            text_content = self.extract_text_from_pdf(pdf_path)
            
            if not text_content.strip():
                return {
                    "answer": "I couldn't extract any text from this PDF. The document might be image-based or corrupted.",
                    "sources": []
                }
            
            # Chunk the text
            chunks = self.chunk_text(text_content)
            
            # Find relevant chunks
            relevant_chunks = self.find_relevant_chunks(chunks, query, top_k=3)
            
            if not relevant_chunks or all(chunk['relevance_score'] == 0 for chunk in relevant_chunks):
                # If no relevant chunks found, use the first few chunks
                relevant_chunks = chunks[:3]
            
            # Prepare context from relevant chunks
            context = "\n\n".join([
                f"[Chunk {chunk['id']} - Page {chunk.get('page', 'Unknown')}]:\n{chunk['text']}"
                for chunk in relevant_chunks
            ])
            
            # Create prompt for Gemini
            prompt = f"""Based on the following document content, please answer the user's question. If the answer cannot be found in the provided content, please say so clearly.

Document Content:
{context}

User Question: {query}

Please provide a clear and accurate answer based only on the information provided in the document content above."""
            
            # Generate response using Gemini
            response = self.model.generate_content(prompt)
            
            # Prepare source information
            sources = [
                f"Page {chunk.get('page', 'Unknown')} (Chunk {chunk['id']})"
                for chunk in relevant_chunks
            ]
            
            return {
                "answer": response.text,
                "sources": sources
            }
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "sources": []
            }
