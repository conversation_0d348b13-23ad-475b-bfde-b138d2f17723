"use client";

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react';
import { cn, validateFile, formatFileSize, type FileValidationResult } from '@/lib/utils';

interface FileUploaderProps {
  onFileSelect: (files: File[]) => void;
  onFileRemove?: (index: number) => void;
  acceptedFileTypes: 'PDF' | 'IMAGE' | 'BOTH';
  multiple?: boolean;
  maxFiles?: number;
  className?: string;
  disabled?: boolean;
  selectedFiles?: File[];
}

interface FileWithValidation extends File {
  validation: FileValidationResult;
  id: string;
}

export default function FileUploader({
  onFileSelect,
  onFileRemove,
  acceptedFileTypes,
  multiple = false,
  maxFiles = 1,
  className,
  disabled = false,
  selectedFiles = [],
}: FileUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [validatedFiles, setValidatedFiles] = useState<FileWithValidation[]>([]);

  // Get accepted file types based on prop
  const getAcceptedTypes = () => {
    switch (acceptedFileTypes) {
      case 'PDF':
        return { 'application/pdf': ['.pdf'] };
      case 'IMAGE':
        return { 
          'image/jpeg': ['.jpg', '.jpeg'],
          'image/png': ['.png'],
          'image/webp': ['.webp']
        };
      case 'BOTH':
        return {
          'application/pdf': ['.pdf'],
          'image/jpeg': ['.jpg', '.jpeg'],
          'image/png': ['.png'],
          'image/webp': ['.webp']
        };
      default:
        return {};
    }
  };

  const validateFiles = (files: File[]): FileWithValidation[] => {
    return files.map((file) => {
      let validation: FileValidationResult;
      
      if (acceptedFileTypes === 'PDF') {
        validation = validateFile(file, 'PDF');
      } else if (acceptedFileTypes === 'IMAGE') {
        validation = validateFile(file, 'IMAGE');
      } else {
        // For 'BOTH', try PDF first, then IMAGE
        const pdfValidation = validateFile(file, 'PDF');
        if (pdfValidation.isValid) {
          validation = pdfValidation;
        } else {
          validation = validateFile(file, 'IMAGE');
        }
      }

      return {
        ...file,
        validation,
        id: Math.random().toString(36).substr(2, 9),
      } as FileWithValidation;
    });
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (disabled) return;

    const newValidatedFiles = validateFiles(acceptedFiles);
    const validFiles = newValidatedFiles.filter(f => f.validation.isValid);
    
    if (multiple) {
      const totalFiles = validatedFiles.length + newValidatedFiles.length;
      if (totalFiles > maxFiles) {
        // Take only the files that fit within the limit
        const remainingSlots = maxFiles - validatedFiles.length;
        const filesToAdd = newValidatedFiles.slice(0, remainingSlots);
        setValidatedFiles(prev => [...prev, ...filesToAdd]);
        onFileSelect([...selectedFiles, ...filesToAdd.filter(f => f.validation.isValid)]);
      } else {
        setValidatedFiles(prev => [...prev, ...newValidatedFiles]);
        onFileSelect([...selectedFiles, ...validFiles]);
      }
    } else {
      setValidatedFiles(newValidatedFiles.slice(0, 1));
      onFileSelect(validFiles.slice(0, 1));
    }
  }, [disabled, multiple, maxFiles, validatedFiles, selectedFiles, onFileSelect]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedTypes(),
    multiple,
    maxFiles,
    disabled,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  });

  const removeFile = (index: number) => {
    const newFiles = validatedFiles.filter((_, i) => i !== index);
    setValidatedFiles(newFiles);
    const validFiles = newFiles.filter(f => f.validation.isValid);
    onFileSelect(validFiles);
    onFileRemove?.(index);
  };

  const getFileTypeText = () => {
    switch (acceptedFileTypes) {
      case 'PDF':
        return 'PDF files';
      case 'IMAGE':
        return 'Image files (JPG, PNG, WEBP)';
      case 'BOTH':
        return 'PDF and Image files';
      default:
        return 'files';
    }
  };

  return (
    <div className={cn("w-full", className)}>
      {/* Drop Zone */}
      <div
        {...getRootProps()}
        className={cn(
          "relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200",
          "hover:border-blue-400 hover:bg-blue-50/50 dark:hover:bg-blue-900/10",
          isDragActive || dragActive
            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : "border-gray-300 dark:border-gray-600",
          disabled && "opacity-50 cursor-not-allowed",
          validatedFiles.length > 0 && "border-green-300 bg-green-50/50 dark:bg-green-900/10"
        )}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          <div className={cn(
            "p-3 rounded-full",
            isDragActive || dragActive
              ? "bg-blue-100 dark:bg-blue-900/30"
              : "bg-gray-100 dark:bg-gray-800"
          )}>
            <Upload className={cn(
              "h-8 w-8",
              isDragActive || dragActive
                ? "text-blue-600 dark:text-blue-400"
                : "text-gray-500 dark:text-gray-400"
            )} />
          </div>
          
          <div>
            <p className="text-lg font-medium text-gray-900 dark:text-white">
              {isDragActive ? 'Drop files here' : 'Choose files or drag and drop'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {getFileTypeText()} • Max {formatFileSize(
                acceptedFileTypes === 'PDF' ? 25 * 1024 * 1024 : 10 * 1024 * 1024
              )}
            </p>
            {multiple && (
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Up to {maxFiles} files
              </p>
            )}
          </div>
        </div>
      </div>

      {/* File List */}
      {validatedFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Selected Files ({validatedFiles.length})
          </h4>
          
          {validatedFiles.map((file, index) => (
            <div
              key={file.id}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg border",
                file.validation.isValid
                  ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20"
                  : "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"
              )}
            >
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className={cn(
                  "p-1 rounded",
                  file.validation.isValid
                    ? "bg-green-100 dark:bg-green-900/30"
                    : "bg-red-100 dark:bg-red-900/30"
                )}>
                  {file.validation.isValid ? (
                    <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatFileSize(file.size)}
                  </p>
                  {!file.validation.isValid && (
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      {file.validation.error}
                    </p>
                  )}
                </div>
              </div>
              
              <button
                onClick={() => removeFile(index)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
                title="Remove file"
              >
                <X className="h-4 w-4 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
