"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Key, Shield, Info, ExternalLink } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { storage, STORAGE_KEYS } from '@/lib/utils';
import { apiService, type ModelInfo } from '@/lib/api';

export default function SettingsPage() {
  const [apiKey, setApiKey] = useState('');
  const [selectedModel, setSelectedModel] = useState('gemini-pro');
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Load saved settings on component mount
  useEffect(() => {
    const savedApiKey = storage.get(STORAGE_KEYS.GEMINI_API_KEY);
    const savedModel = storage.get(STORAGE_KEYS.SELECTED_MODEL);
    
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
    if (savedModel) {
      setSelectedModel(savedModel);
    }
  }, []);

  // Load available models
  const loadModels = async () => {
    setLoading(true);
    try {
      const modelList = await apiService.getModels();
      setModels(modelList);
    } catch (error) {
      console.error('Failed to load models:', error);
    } finally {
      setLoading(false);
    }
  };

  // Test API connection
  const testConnection = async () => {
    if (!apiKey.trim()) {
      setConnectionStatus('error');
      return;
    }

    setTestingConnection(true);
    setConnectionStatus('idle');

    try {
      // Test the connection by trying to load models with the user's API key
      // Note: This would require a test endpoint in the backend
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setConnectionStatus('success');
    } catch (error) {
      setConnectionStatus('error');
    } finally {
      setTestingConnection(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    setSaving(true);
    
    try {
      // Save to localStorage
      if (apiKey.trim()) {
        storage.set(STORAGE_KEYS.GEMINI_API_KEY, apiKey.trim());
      } else {
        storage.remove(STORAGE_KEYS.GEMINI_API_KEY);
      }
      
      storage.set(STORAGE_KEYS.SELECTED_MODEL, selectedModel);
      
      // Show success feedback
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  // Load models on component mount
  useEffect(() => {
    loadModels();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Tools</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configure your API settings and preferences for AI-powered features.
          </p>
        </div>

        <div className="space-y-6">
          {/* API Key Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Gemini API Key</span>
              </CardTitle>
              <CardDescription>
                Enter your Google Gemini API key to enable AI-powered features like Chat with PDF and Summarization.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-800 dark:text-blue-200">
                    <p className="font-medium mb-1">Privacy Notice</p>
                    <p>
                      Your API key is stored locally in your browser and is only used to communicate directly with Google's Gemini API. 
                      It is never sent to our servers for non-AI operations.
                    </p>
                  </div>
                </div>
              </div>

              <Input
                type="password"
                label="API Key"
                placeholder="Enter your Gemini API key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                helperText="Get your API key from Google AI Studio"
              />

              <div className="flex items-center space-x-3">
                <Button
                  onClick={testConnection}
                  loading={testingConnection}
                  variant="outline"
                  size="sm"
                >
                  Test Connection
                </Button>
                
                {connectionStatus === 'success' && (
                  <span className="text-green-600 dark:text-green-400 text-sm flex items-center space-x-1">
                    <Shield className="h-4 w-4" />
                    <span>Connection successful</span>
                  </span>
                )}
                
                {connectionStatus === 'error' && (
                  <span className="text-red-600 dark:text-red-400 text-sm">
                    Connection failed. Please check your API key.
                  </span>
                )}
              </div>

              <div className="pt-2">
                <a
                  href="https://makersuite.google.com/app/apikey"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:underline text-sm"
                >
                  <span>Get your API key from Google AI Studio</span>
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </CardContent>
          </Card>

          {/* Model Selection */}
          <Card>
            <CardHeader>
              <CardTitle>AI Model Selection</CardTitle>
              <CardDescription>
                Choose which Gemini model to use for AI-powered features.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Selected Model
                </label>
                
                {loading ? (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Loading available models...
                  </div>
                ) : (
                  <select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {models.length > 0 ? (
                      models.map((model) => (
                        <option key={model.name} value={model.name}>
                          {model.name} - {model.description}
                        </option>
                      ))
                    ) : (
                      <option value="gemini-pro">gemini-pro (Default)</option>
                    )}
                  </select>
                )}
                
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Different models may have varying capabilities and response times.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={saveSettings}
              loading={saving}
              size="lg"
            >
              Save Settings
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
