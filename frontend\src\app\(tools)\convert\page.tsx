"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowLeft, Download, FileImage, FileText, ArrowRight } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import FileUploader from '@/components/FileUploader';
import { downloadBlob, generateFilename, getFileExtension } from '@/lib/utils';
import { PDFDocument, rgb } from 'pdf-lib';

type ConversionMode = 'image-to-pdf' | 'pdf-to-image';

export default function ConvertPage() {
  const [conversionMode, setConversionMode] = useState<ConversionMode>('image-to-pdf');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = (files: File[]) => {
    setSelectedFiles(files);
    setError(null);
  };

  const convertImagesToPDF = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select at least one image file.');
      return;
    }

    setProcessing(true);
    setError(null);

    try {
      const pdfDoc = await PDFDocument.create();

      for (const file of selectedFiles) {
        const arrayBuffer = await file.arrayBuffer();
        const fileExtension = getFileExtension(file.name);
        
        let image;
        if (fileExtension === 'jpg' || fileExtension === 'jpeg') {
          image = await pdfDoc.embedJpg(arrayBuffer);
        } else if (fileExtension === 'png') {
          image = await pdfDoc.embedPng(arrayBuffer);
        } else {
          // For WEBP and other formats, we'll need to convert to canvas first
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();
          
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
          });

          canvas.width = img.width;
          canvas.height = img.height;
          ctx?.drawImage(img, 0, 0);
          
          const pngDataUrl = canvas.toDataURL('image/png');
          const pngArrayBuffer = await fetch(pngDataUrl).then(res => res.arrayBuffer());
          image = await pdfDoc.embedPng(pngArrayBuffer);
          
          URL.revokeObjectURL(img.src);
        }

        // Calculate page size to fit image while maintaining aspect ratio
        const { width, height } = image.scale(1);
        const maxWidth = 595; // A4 width in points
        const maxHeight = 842; // A4 height in points
        
        let pageWidth = width;
        let pageHeight = height;
        
        if (width > maxWidth || height > maxHeight) {
          const scaleX = maxWidth / width;
          const scaleY = maxHeight / height;
          const scale = Math.min(scaleX, scaleY);
          
          pageWidth = width * scale;
          pageHeight = height * scale;
        }

        const page = pdfDoc.addPage([pageWidth, pageHeight]);
        page.drawImage(image, {
          x: 0,
          y: 0,
          width: pageWidth,
          height: pageHeight,
        });
      }

      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });
      
      const filename = selectedFiles.length === 1 
        ? generateFilename(selectedFiles[0].name.replace(/\.[^/.]+$/, ''), 'converted.pdf')
        : 'converted_images.pdf';
      
      downloadBlob(blob, filename);

    } catch (err) {
      console.error('Error converting images to PDF:', err);
      setError('Failed to convert images to PDF. Please ensure all files are valid images.');
    } finally {
      setProcessing(false);
    }
  };

  const convertPDFToImages = async () => {
    if (selectedFiles.length === 0) {
      setError('Please select a PDF file.');
      return;
    }

    const pdfFile = selectedFiles[0];
    setProcessing(true);
    setError(null);

    try {
      // For PDF to image conversion, we'll use a canvas-based approach
      const arrayBuffer = await pdfFile.arrayBuffer();
      const pdfDoc = await PDFDocument.load(arrayBuffer);
      const pageCount = pdfDoc.getPageCount();

      // Note: This is a simplified implementation
      // In a real application, you'd use a library like pdf2pic or PDF.js
      for (let i = 0; i < pageCount; i++) {
        // Create a single-page PDF for each page
        const singlePagePdf = await PDFDocument.create();
        const [page] = await singlePagePdf.copyPages(pdfDoc, [i]);
        singlePagePdf.addPage(page);
        
        const singlePageBytes = await singlePagePdf.save();
        const blob = new Blob([singlePageBytes], { type: 'application/pdf' });
        
        // For now, we'll download the individual PDF pages
        // In a real implementation, you'd render these to canvas and convert to images
        const filename = generateFilename(
          pdfFile.name.replace('.pdf', ''), 
          `page_${i + 1}.pdf`
        );
        downloadBlob(blob, filename);
      }

      // Show a message about the limitation
      setError('Note: This demo creates individual PDF pages. In a full implementation, these would be converted to image files (PNG/JPG).');

    } catch (err) {
      console.error('Error converting PDF to images:', err);
      setError('Failed to convert PDF to images. Please ensure the file is a valid PDF.');
    } finally {
      setProcessing(false);
    }
  };

  const handleConvert = () => {
    if (conversionMode === 'image-to-pdf') {
      convertImagesToPDF();
    } else {
      convertPDFToImages();
    }
  };

  const getAcceptedFileTypes = () => {
    return conversionMode === 'image-to-pdf' ? 'IMAGE' : 'PDF';
  };

  const getMultipleFiles = () => {
    return conversionMode === 'image-to-pdf';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link 
              href="/"
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
              <span>Back to Tools</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FileImage className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Convert Files
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Convert between PDF and image formats. Combine multiple images into a PDF or extract PDF pages as images.
          </p>
        </div>

        <div className="space-y-6">
          {/* Conversion Mode Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Conversion Mode</CardTitle>
              <CardDescription>
                Choose the type of conversion you want to perform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setConversionMode('image-to-pdf')}
                  className={`p-6 rounded-lg border-2 transition-all ${
                    conversionMode === 'image-to-pdf'
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-3 mb-3">
                    <FileImage className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                    <FileText className="h-8 w-8 text-red-600 dark:text-red-400" />
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    Images to PDF
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Convert JPG, PNG, WEBP images into a single PDF document
                  </p>
                </button>

                <button
                  onClick={() => setConversionMode('pdf-to-image')}
                  className={`p-6 rounded-lg border-2 transition-all ${
                    conversionMode === 'pdf-to-image'
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="flex items-center justify-center space-x-3 mb-3">
                    <FileText className="h-8 w-8 text-red-600 dark:text-red-400" />
                    <ArrowRight className="h-5 w-5 text-gray-400" />
                    <FileImage className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    PDF to Images
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Extract PDF pages as individual image files
                  </p>
                </button>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle>
                {conversionMode === 'image-to-pdf' ? 'Select Images' : 'Select PDF'}
              </CardTitle>
              <CardDescription>
                {conversionMode === 'image-to-pdf' 
                  ? 'Choose one or more image files to convert to PDF.'
                  : 'Choose a PDF file to convert to images.'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploader
                onFileSelect={handleFileSelect}
                acceptedFileTypes={getAcceptedFileTypes()}
                multiple={getMultipleFiles()}
                maxFiles={getMultipleFiles() ? 20 : 1}
                selectedFiles={selectedFiles}
              />
            </CardContent>
          </Card>

          {/* File Preview */}
          {selectedFiles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Selected Files</CardTitle>
                <CardDescription>
                  {conversionMode === 'image-to-pdf' 
                    ? 'Images will be combined into a single PDF in the order shown.'
                    : 'PDF will be converted to individual image files.'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={`${file.name}-${index}`}
                      className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    >
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded">
                        {conversionMode === 'image-to-pdf' ? (
                          <FileImage className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <FileText className="h-4 w-4 text-red-600 dark:text-red-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {conversionMode === 'image-to-pdf' ? `${index + 1}. ` : ''}{file.name}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          {/* Convert Button */}
          <div className="flex justify-center">
            <Button
              onClick={handleConvert}
              loading={processing}
              disabled={selectedFiles.length === 0}
              size="lg"
              className="min-w-[200px]"
            >
              <Download className="mr-2 h-5 w-5" />
              {processing ? 'Converting...' : 'Convert & Download'}
            </Button>
          </div>

          {/* Info */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Conversion Details:
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              {conversionMode === 'image-to-pdf' ? (
                <>
                  <li>• Supported formats: JPG, PNG, WEBP</li>
                  <li>• Images are automatically resized to fit PDF pages</li>
                  <li>• Original aspect ratios are preserved</li>
                  <li>• Multiple images create a multi-page PDF</li>
                </>
              ) : (
                <>
                  <li>• Each PDF page becomes a separate image file</li>
                  <li>• High-quality image extraction</li>
                  <li>• Maintains original page dimensions</li>
                  <li>• Note: This demo shows the concept - full implementation would generate actual image files</li>
                </>
              )}
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
