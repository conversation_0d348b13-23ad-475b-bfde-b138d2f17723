"""
PDF Utilities Router - Handles PDF manipulation operations
"""

import fitz  # PyMuPDF
import os
import tempfile
import logging
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from fastapi.responses import FileResponse
from typing import Optional

from app.models import ProtectRequest, UnlockRequest, SuccessResponse, ErrorResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/compress")
async def compress_pdf(file: UploadFile = File(...)):
    """
    Compress a PDF file to reduce its size
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are supported"
            )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_input:
            content = await file.read()
            temp_input.write(content)
            temp_input_path = temp_input.name
        
        # Create output file path
        with tempfile.NamedTemporaryFile(delete=False, suffix='_compressed.pdf') as temp_output:
            temp_output_path = temp_output.name
        
        try:
            # Open and compress PDF
            doc = fitz.open(temp_input_path)
            
            # Apply compression settings
            doc.save(
                temp_output_path,
                garbage=4,  # Remove unused objects
                deflate=True,  # Compress streams
                clean=True,  # Clean up document
                linear=True,  # Linearize for web viewing
            )
            doc.close()
            
            # Get file sizes for comparison
            original_size = os.path.getsize(temp_input_path)
            compressed_size = os.path.getsize(temp_output_path)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            logger.info(f"PDF compressed: {original_size} -> {compressed_size} bytes ({compression_ratio:.1f}% reduction)")
            
            # Return compressed file
            return FileResponse(
                temp_output_path,
                media_type="application/pdf",
                filename=f"compressed_{file.filename}",
                headers={
                    "X-Original-Size": str(original_size),
                    "X-Compressed-Size": str(compressed_size),
                    "X-Compression-Ratio": f"{compression_ratio:.1f}%"
                }
            )
            
        finally:
            # Clean up input file
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            
    except Exception as e:
        logger.error(f"Error compressing PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/protect")
async def protect_pdf(
    file: UploadFile = File(...),
    password: str = Form(...)
):
    """
    Add password protection to a PDF file
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are supported"
            )
        
        if not password or len(password.strip()) < 1:
            raise HTTPException(
                status_code=400,
                detail="Password cannot be empty"
            )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_input:
            content = await file.read()
            temp_input.write(content)
            temp_input_path = temp_input.name
        
        # Create output file path
        with tempfile.NamedTemporaryFile(delete=False, suffix='_protected.pdf') as temp_output:
            temp_output_path = temp_output.name
        
        try:
            # Open PDF and add password protection
            doc = fitz.open(temp_input_path)
            
            # Set encryption with password
            perm = int(
                fitz.PDF_PERM_ACCESSIBILITY |  # Allow accessibility
                fitz.PDF_PERM_PRINT |         # Allow printing
                fitz.PDF_PERM_COPY |          # Allow copying
                fitz.PDF_PERM_ANNOTATE        # Allow annotations
            )
            
            doc.save(
                temp_output_path,
                encryption=fitz.PDF_ENCRYPT_AES_256,  # Use AES-256 encryption
                user_pw=password,  # User password
                owner_pw=password,  # Owner password (same as user for simplicity)
                permissions=perm
            )
            doc.close()
            
            logger.info(f"PDF protected with password")
            
            # Return protected file
            return FileResponse(
                temp_output_path,
                media_type="application/pdf",
                filename=f"protected_{file.filename}"
            )
            
        finally:
            # Clean up input file
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            
    except Exception as e:
        logger.error(f"Error protecting PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/unlock")
async def unlock_pdf(
    file: UploadFile = File(...),
    password: str = Form(...)
):
    """
    Remove password protection from a PDF file
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400, 
                detail="Only PDF files are supported"
            )
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_input:
            content = await file.read()
            temp_input.write(content)
            temp_input_path = temp_input.name
        
        # Create output file path
        with tempfile.NamedTemporaryFile(delete=False, suffix='_unlocked.pdf') as temp_output:
            temp_output_path = temp_output.name
        
        try:
            # Try to open PDF with password
            try:
                doc = fitz.open(temp_input_path)
                if doc.needs_pass:
                    if not doc.authenticate(password):
                        raise HTTPException(
                            status_code=401,
                            detail="Incorrect password provided"
                        )
            except Exception as auth_error:
                raise HTTPException(
                    status_code=401,
                    detail="Failed to authenticate PDF with provided password"
                )
            
            # Save unlocked version (without encryption)
            doc.save(temp_output_path)
            doc.close()
            
            logger.info(f"PDF unlocked successfully")
            
            # Return unlocked file
            return FileResponse(
                temp_output_path,
                media_type="application/pdf",
                filename=f"unlocked_{file.filename}"
            )
            
        finally:
            # Clean up input file
            if os.path.exists(temp_input_path):
                os.unlink(temp_input_path)
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error unlocking PDF: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
